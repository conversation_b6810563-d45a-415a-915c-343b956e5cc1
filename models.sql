CREATE TABLE article_property (
    art_id INTEGER NOT NULL,
    propertykeyvalue_id INTEGER NOT NULL,
    sort INTEGER NOT NULL,
    CONSTRAINT pk_article_property PRIMARY KEY (art_id, propertykeyvalue_id)
);

-- =======================================================================
CREATE TABLE campaign_property (
    camp_id INTEGER NOT NULL,
    propertykeyvalue_id INTEGER NOT NULL,
    sort INTEGER NOT NULL,
    CONSTRAINT pk_campaign_property PRIMARY KEY (camp_id, propertykeyvalue_id)
);

-- =======================================================================

CREATE TABLE propertykey (
    propertykey_id SERIAL NOT NULL,
    propertykey_name VARCHAR(100) NOT NULL,
    sort INTEGER NOT NULL,
    keytype SMALLINT NOT NULL,
    CONSTRAINT pk_propertykey PRIMARY KEY (propertykey_id),
    CONSTRAINT uk_propertykey_name_keytype UNIQUE (propertykey_name, keytype)
);

-- =======================================================================

CREATE TABLE propertykey_translation (
    propertykey_id INTEGER NOT NULL,
    lang_id SMALLINT NOT NULL,
    propertykey_name_translation VARCHAR(100) NOT NULL,
    propertykey_desc_translation TEXT NOT NULL,
    CONSTRAINT pk_propertykey_name PRIMARY KEY (propertykey_id, lang_id)
);

-- =======================================================================
CREATE TABLE propertyvalue (
    propertyvalue_id SERIAL NOT NULL,
    propertyvalue TEXT NOT NULL,
    CONSTRAINT pk_propertyvalue PRIMARY KEY (propertyvalue_id)
);

-- =======================================================================
CREATE TABLE propertyvalue_translation (
    propertyvalue_id INTEGER NOT NULL,
    lang_id SMALLINT NOT NULL,
    propertyvalue_translation TEXT NOT NULL,
    propertyvalue_desc_translation TEXT NOT NULL,
    CONSTRAINT pk_propertyvalue_name PRIMARY KEY (propertyvalue_id, lang_id)
);

-- =======================================================================
CREATE TABLE propertykey_value (
    propertykeyvalue_id SERIAL NOT NULL,
    propertykey_id INTEGER NOT NULL,
    propertyvalue_id INTEGER NOT NULL,
    CONSTRAINT pk_propertykey_value PRIMARY KEY (propertykeyvalue_id),
    CONSTRAINT uk_propertykey_value UNIQUE (propertykey_id, propertyvalue_id)
);

-- =======================================================================
-- Table type for property input parameters
-- =======================================================================
CREATE TYPE property_input_type AS (
    propertykey_name VARCHAR(100),
    propertyvalue TEXT,
    lang_id SMALLINT,
    propertykey_name_translation VARCHAR(100),
    propertyvalue_translation TEXT
);

-- =======================================================================
-- Stored procedure to upsert entity properties
-- =======================================================================
CREATE OR REPLACE FUNCTION upsert_entity_properties(
    p_keytype SMALLINT,
    p_entity_id INTEGER,
    p_properties property_input_type[]
)
RETURNS VOID AS $$
DECLARE
    property_rec property_input_type;
    v_propertykey_id INTEGER;
    v_propertyvalue_id INTEGER;
    v_propertykeyvalue_id INTEGER;
    v_sort INTEGER;
BEGIN
    -- Loop through each property in the input array
    FOREACH property_rec IN ARRAY p_properties
    LOOP
        -- 1. Upsert propertykey
        -- First try to get existing propertykey_id
        SELECT propertykey_id INTO v_propertykey_id
        FROM propertykey
        WHERE propertykey_name = property_rec.propertykey_name
        AND keytype = p_keytype;

        -- If not found, insert new propertykey
        IF v_propertykey_id IS NULL THEN
            INSERT INTO propertykey (propertykey_name, sort, keytype)
            VALUES (property_rec.propertykey_name, 0, p_keytype)
            RETURNING propertykey_id INTO v_propertykey_id;
        END IF;

        -- 2. Upsert propertykey_translation if translation data provided
        IF property_rec.propertykey_name_translation IS NOT NULL AND property_rec.lang_id IS NOT NULL THEN
            INSERT INTO propertykey_translation (propertykey_id, lang_id, propertykey_name_translation, propertykey_desc_translation)
            VALUES (v_propertykey_id, property_rec.lang_id, property_rec.propertykey_name_translation, '')
            ON CONFLICT (propertykey_id, lang_id) DO UPDATE SET
                propertykey_name_translation = EXCLUDED.propertykey_name_translation;
        END IF;

        -- 3. Check if this propertykey-propertyvalue combination already exists for this entity
        IF p_keytype = 1 THEN -- Article properties
            SELECT pkv.propertykeyvalue_id, pkv.propertyvalue_id
            INTO v_propertykeyvalue_id, v_propertyvalue_id
            FROM propertykey_value pkv
            INNER JOIN propertyvalue pv ON pkv.propertyvalue_id = pv.propertyvalue_id
            INNER JOIN article_property ap ON pkv.propertykeyvalue_id = ap.propertykeyvalue_id
            WHERE pkv.propertykey_id = v_propertykey_id
            AND pv.propertyvalue = property_rec.propertyvalue
            AND ap.art_id = p_entity_id;

        ELSIF p_keytype = 2 THEN -- Campaign properties
            SELECT pkv.propertykeyvalue_id, pkv.propertyvalue_id
            INTO v_propertykeyvalue_id, v_propertyvalue_id
            FROM propertykey_value pkv
            INNER JOIN propertyvalue pv ON pkv.propertyvalue_id = pv.propertyvalue_id
            INNER JOIN campaign_property cp ON pkv.propertykeyvalue_id = cp.propertykeyvalue_id
            WHERE pkv.propertykey_id = v_propertykey_id
            AND pv.propertyvalue = property_rec.propertyvalue
            AND cp.camp_id = p_entity_id;
        END IF;

        -- If not found for this entity, create new propertyvalue and propertykey_value
        IF v_propertykeyvalue_id IS NULL THEN
            -- Insert new propertyvalue (always create new one to avoid conflicts with other entities)
            INSERT INTO propertyvalue (propertyvalue)
            VALUES (property_rec.propertyvalue)
            RETURNING propertyvalue_id INTO v_propertyvalue_id;

            -- Insert new propertykey_value
            INSERT INTO propertykey_value (propertykey_id, propertyvalue_id)
            VALUES (v_propertykey_id, v_propertyvalue_id)
            RETURNING propertykeyvalue_id INTO v_propertykeyvalue_id;
        END IF;

        -- 4. Upsert propertyvalue_translation if translation data provided
        IF property_rec.propertyvalue_translation IS NOT NULL AND property_rec.lang_id IS NOT NULL THEN
            INSERT INTO propertyvalue_translation (propertyvalue_id, lang_id, propertyvalue_translation, propertyvalue_desc_translation)
            VALUES (v_propertyvalue_id, property_rec.lang_id, property_rec.propertyvalue_translation, '')
            ON CONFLICT (propertyvalue_id, lang_id) DO UPDATE SET
                propertyvalue_translation = EXCLUDED.propertyvalue_translation;
        END IF;

        -- 5. Upsert entity property (only if we created a new propertykeyvalue_id or it doesn't exist for this entity)
        IF p_keytype = 1 THEN -- Article properties
            -- Check if this article_property relationship already exists
            IF NOT EXISTS (
                SELECT 1 FROM article_property
                WHERE art_id = p_entity_id AND propertykeyvalue_id = v_propertykeyvalue_id
            ) THEN
                -- Get next sort order
                SELECT COALESCE(MAX(sort), 0) + 1 INTO v_sort
                FROM article_property
                WHERE art_id = p_entity_id;

                -- Insert article_property
                INSERT INTO article_property (art_id, propertykeyvalue_id, sort)
                VALUES (p_entity_id, v_propertykeyvalue_id, v_sort);
            END IF;

        ELSIF p_keytype = 2 THEN -- Campaign properties
            -- Check if this campaign_property relationship already exists
            IF NOT EXISTS (
                SELECT 1 FROM campaign_property
                WHERE camp_id = p_entity_id AND propertykeyvalue_id = v_propertykeyvalue_id
            ) THEN
                -- Get next sort order
                SELECT COALESCE(MAX(sort), 0) + 1 INTO v_sort
                FROM campaign_property
                WHERE camp_id = p_entity_id;

                -- Insert campaign_property
                INSERT INTO campaign_property (camp_id, propertykeyvalue_id, sort)
                VALUES (p_entity_id, v_propertykeyvalue_id, v_sort);
            END IF;
        END IF;

    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =======================================================================
-- Example usage:
-- =======================================================================
/*
This stored procedure ensures that property values are scoped to specific entities.
If the same property value exists for different entities, separate propertyvalue
records will be created to avoid conflicts.

-- Example 1: Insert/Update article properties (keytype = 1)
SELECT upsert_entity_properties(
    1, -- keytype for articles
    123, -- article ID
    ARRAY[
        ROW('color', 'red', 1, 'Color', 'Rouge')::property_input_type,
        ROW('size', 'large', 1, 'Size', 'Grand')::property_input_type,
        ROW('brand', 'Nike', 2, 'Brand', 'Nike')::property_input_type
    ]
);

-- Example 2: Insert/Update campaign properties (keytype = 2)
-- Even if 'red' was used for article above, a new propertyvalue will be created for campaign
SELECT upsert_entity_properties(
    2, -- keytype for campaigns
    456, -- campaign ID
    ARRAY[
        ROW('theme_color', 'red', 1, 'Theme Color', 'Rouge')::property_input_type,
        ROW('budget', '10000', 1, 'Budget', '10000')::property_input_type
    ]
);

-- Example 3: Insert properties without translations
SELECT upsert_entity_properties(
    1, -- keytype for articles
    789, -- article ID
    ARRAY[
        ROW('material', 'cotton', NULL, NULL, NULL)::property_input_type,
        ROW('weight', '500g', NULL, NULL, NULL)::property_input_type
    ]
);

-- Example 4: Update existing property for same entity
-- This will reuse existing propertyvalue if same key-value exists for this entity
SELECT upsert_entity_properties(
    1, -- keytype for articles
    123, -- same article ID as Example 1
    ARRAY[
        ROW('color', 'red', 1, 'Color', 'Rouge')::property_input_type -- Will reuse existing
    ]
);
*/